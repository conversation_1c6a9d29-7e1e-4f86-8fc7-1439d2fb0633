@charset "UTF-8";
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, font, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, ar, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  font-size: 100%;
}

@font-face {
  font-family: "Noto Sans TC";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(../vendor/fonts/NotoSansTC-Regular.ttf) format("truetype");
}
@font-face {
  font-family: "Noto Sans TC";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(../vendor/fonts/NotoSansTC-Medium.ttf) format("truetype");
}
@font-face {
  font-family: "Noto Sans TC";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(../vendor/fonts/NotoSansTC-Bold.ttf) format("truetype");
}
.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

@-webkit-keyframes fadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
.fadeInDown {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
}

@-webkit-keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale(0);
            transform: scale(0);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale(0);
            transform: scale(0);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
.zoomIn {
  -webkit-animation-name: zoomIn;
  animation-name: zoomIn;
}

@-webkit-keyframes heartbeat {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}

@keyframes heartbeat {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
.heartbeat {
  -webkit-animation-name: heartbeat;
  animation-name: heartbeat;
}

.btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 0;
  width: 340px;
  height: 116px;
  background-size: 100% auto;
  background-position: center center;
  background-repeat: no-repeat;
}
@media (max-width: 1399px) {
  .btn {
    width: 300px;
  }
}
@media (max-width: 1199px) {
  .btn {
    width: 250px;
    height: 86px;
  }
}
@media (max-width: 991px) {
  .btn {
    width: 340px;
    height: 116px;
    margin-top: 15px;
  }
  .btn:first-child {
    margin-top: 0;
  }
}
@media (max-width: 413px) {
  .btn {
    width: 250px;
    height: 86px;
  }
}
@media (hover: hover) {
  .btn:hover {
    -webkit-animation: heartbeat 1.5s ease infinite;
            animation: heartbeat 1.5s ease infinite;
  }
}

body {
  font-family: "Noto Sans TC", "微軟正黑體", sans-serif;
  color: #000000;
  margin: 0 auto;
  font-weight: 400;
  line-height: 1.2;
  position: relative;
  overflow-x: hidden;
}

html {
  overflow-x: hidden;
}

select, button, textarea, input {
  font-family: "Noto Sans TC", "微軟正黑體", sans-serif;
  outline: none;
}

* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  -webkit-text-size-adjust: none;
}

table {
  border-collapse: collapse;
}

a, a:focus {
  cursor: pointer;
  text-decoration: none;
  -webkit-transition: color 300ms;
  transition: color 300ms;
}

a:active {
  outline: none;
}

img {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  max-width: 100%;
}

.container {
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
@media (min-width: 768px) {
  .container {
    width: 750px;
    padding-right: 0px;
    padding-left: 0px;
  }
}
@media (min-width: 992px) {
  .container {
    width: 850px;
  }
}
@media (min-width: 1200px) {
  .container {
    width: 1050px;
  }
}
@media (min-width: 1400px) {
  .container {
    width: 1350px;
  }
}

.header {
  background: #ffffff;
  -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.08);
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.08);
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1030;
}

.navbar {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  height: 75px;
}
.navbar__logo {
  display: block;
  margin: 0 auto;
  width: 192px;
}
@media (max-width: 991px) {
  .navbar__logo {
    width: 220px;
    position: relative;
    z-index: 3;
  }
}
@media (max-width: 480px) {
  .navbar__logo {
    width: 200px;
  }
}

.footer {
  text-align: center;
  background: url("../images/footer-bg.jpg"), #454545;
  background-size: auto;
  background-position: center top;
  background-repeat: no-repeat;
  padding-top: 30px;
  padding-bottom: 21px;
  font-size: 0.9375em;
  letter-spacing: 0.075em;
  color: #ffffff;
  text-align: center;
  line-height: 1.6;
}

.kv {
  height: 980px;
  position: relative;
  background-image: url("../images/kv_bottom.png"), url("../images/kv_people.png"), url("../images/kv_bg.jpg");
  background-size: 100% auto, 1240px auto, cover;
  background-position: bottom, calc(50% - 70px) calc(100% - 10px), center;
  background-repeat: no-repeat, no-repeat, no-repeat;
}
@media (max-width: 480px) {
  .kv {
    padding-top: 50px;
  }
}
.kv__title {
  display: block;
  width: 719px;
  height: auto;
  position: relative;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-42%, -74%);
          transform: translate(-42%, -74%);
}
@media (max-width: 767px) {
  .kv__title {
    margin-top: 57px;
  }
}
.kv__title--desktop {
  display: block;
}
@media (max-width: 767px) {
  .kv__title--desktop {
    display: none;
  }
}
.kv__title--mobile {
  display: none;
}
@media (max-width: 767px) {
  .kv__title--mobile {
    display: block;
  }
}
.kv__subtitle {
  position: relative;
  text-align: center;
  margin-top: 30px;
  color: #3c7d5a;
  font-size: 1.875em;
  font-weight: 700;
  background-color: #ffffff;
  padding: 10px 20px;
  border-radius: 30px;
  display: inline-block;
}
.kv__subtitle-text--highlight {
  padding: 0 10px;
  color: #ffeeb9;
  background-color: #3c7d5a;
  border-radius: 30px;
}

.s1 {
  width: 100%;
  height: 100%;
  position: relative;
  padding-top: 45px;
  background: url("../images/s1-bg.jpg");
  background-size: 100% auto;
  background-position: center top;
  background-repeat: repeat-y;
}
.s1__block {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  width: calc(100% + 12px);
  margin-left: -6px;
  margin-right: -6px;
  text-align: center;
  position: relative;
  margin-top: 35px;
}
.s1__item {
  padding-left: 6px;
  padding-right: 6px;
}
@media (max-width: 1399px) {
  .s1__item {
    width: 351px;
  }
}
@media (max-width: 1199px) {
  .s1__item {
    width: 287px;
  }
}
@media (max-width: 991px) {
  .s1__item {
    width: 250px;
  }
}
@media (max-width: 767px) {
  .s1__item {
    padding-bottom: 0;
  }
}
.s1__item-text {
  display: block;
  color: #0a3a6e;
  line-height: 1.6em;
  margin-top: 10px;
  padding-left: 20px;
  padding-right: 50px;
  text-align: left;
  position: relative;
}
@media (max-width: 1399px) {
  .s1__item-text {
    padding-left: 10px;
  }
}
@media (max-width: 1199px) {
  .s1__item-text {
    padding-right: 10px;
  }
}
.s1__item-text--title {
  font-size: 1.125em;
  font-weight: 700;
  line-height: 1.6em;
  margin-top: 10px;
  text-align: left;
}
.s1__item-text--subtitle {
  font-size: 1.125em;
  line-height: 1.6em;
  margin-top: 10px;
  text-align: left;
}
.s1__item-text--icon {
  display: inline-block;
  width: 17px;
  height: 16px;
  margin-top: 2px;
  background: url("../images/s1__icon-01.png");
  background-size: 100% auto;
  background-position: center center;
}
.s1__item-text-box {
  max-height: 4.5em;
  line-height: 1.5em;
  overflow: hidden;
  position: relative;
  -webkit-transition: max-height 0.3s ease;
  transition: max-height 0.3s ease;
}
.s1__item-text-box.hide::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0.4em;
  background: -webkit-gradient(linear, left bottom, left top, from(#f7f7f7), to(transparent));
  background: linear-gradient(to top, #f7f7f7, transparent);
  pointer-events: none;
}
.s1__item-text .toggle-btn {
  color: #0a3a6e;
  border: 1px solid #0a3a6e;
  padding: 5px;
  font-size: 14px;
  cursor: pointer;
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 3;
  width: 40px;
  line-height: 100%;
  text-align: center;
}
@media (max-width: 1199px) {
  .s1__item-text .toggle-btn {
    position: relative;
    right: auto;
    bottom: auto;
    width: 100%;
    padding: 10px;
    margin-top: 10px;
  }
}
.s1__contents {
  width: 100%;
  height: 100%;
  margin-top: -105px;
  margin-left: auto;
  margin-right: auto;
}
.s1__banner {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  margin: 60px 0 66px;
}
@media (max-width: 1199px) {
  .s1__banner {
    margin-bottom: 30px;
  }
}
.s1__banner img {
  margin-left: auto;
  margin-right: auto;
  padding-left: 124px;
  padding-right: 124px;
}
@media (max-width: 991px) {
  .s1__banner img {
    padding-left: 40px;
    padding-right: 40px;
  }
}
@media (max-width: 767px) {
  .s1__banner img {
    padding-left: 12px;
    padding-right: 12px;
  }
}
.s1__banner::before {
  content: "";
  position: absolute;
  top: -120px;
  left: -50px;
  width: 512px;
  height: 479px;
  z-index: 1;
  background: url("../images/s1__light-left.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 100% auto;
  pointer-events: none;
}
@media (max-width: 1199px) {
  .s1__banner::before {
    width: 400px;
    left: -20px;
  }
}
@media (max-width: 991px) {
  .s1__banner::before {
    left: -70px;
  }
}
@media (max-width: 767px) {
  .s1__banner::before {
    width: 300px;
    top: -190px;
    left: -50px;
  }
}
.s1__banner::after {
  content: "";
  position: absolute;
  top: -120px;
  right: -50px;
  width: 512px;
  height: 479px;
  background: url("../images/s1__light-right.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 100% auto;
  pointer-events: none;
}
@media (max-width: 1199px) {
  .s1__banner::after {
    width: 400px;
    right: -20px;
  }
}
@media (max-width: 991px) {
  .s1__banner::after {
    right: -70px;
  }
}
@media (max-width: 767px) {
  .s1__banner::after {
    width: 300px;
    top: -190px;
    right: -50px;
  }
}
.s1__btn {
  margin-top: auto 0px;
}
@media (max-width: 1399px) {
  .s1__btn {
    width: 300px;
  }
}
@media (max-width: 1199px) {
  .s1__btn {
    width: 250px;
  }
}
@media (max-width: 991px) {
  .s1__btn {
    width: 340px;
    margin-top: 10px;
  }
}
@media (max-width: 413px) {
  .s1__btn {
    width: 250px;
  }
}
.s1__btn-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0px auto;
  padding-bottom: 75px;
  justify-content: center;
  gap: 65px;
}
@media (max-width: 991px) {
  .s1__btn-wrap {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 0;
  }
}
@media (max-width: 767px) {
  .s1__btn-wrap {
    padding-bottom: 60px;
  }
}
.s1__btn-login {
  background-image: url("../images/s1__btn-login.png");
}
.s1__btn-home {
  background-image: url("../images/s1__btn-home.png");
}

.slick-prev {
  width: 32px;
  height: 32px;
  right: 37px;
  top: -40px;
}
@media (max-width: 767px) {
  .slick-prev {
    width: 28px;
    height: 28px;
    left: 5px;
    top: -40px;
  }
}
.slick-next {
  width: 32px;
  height: 32px;
  right: 5px;
  top: -40px;
}
@media (max-width: 767px) {
  .slick-next {
    width: 28px;
    height: 28px;
    left: 33px;
    top: -40px;
  }
}

.s2 {
  padding-top: 90px;
  padding-bottom: 65px;
  background-image: url("../images/s2-bg.jpg");
  background-position: center top;
  background-repeat: no-repeat;
}
@media (max-width: 1199px) {
  .s2 {
    background-position: left calc(50vw - 900px) top;
  }
}
@media (max-width: 991px) {
  .s2 {
    padding-top: 1px;
    padding-top: 60px;
    padding-bottom: 60px;
  }
}
@media (max-width: 767px) {
  .s2 {
    background-size: cover;
    background-position: left top;
  }
}
.s2__title {
  width: 308px;
  height: 61px;
  background: url("../images/s2-title.png");
  background-size: 100% auto;
  background-position: center top;
  background-repeat: no-repeat;
  margin-top: 70px;
  text-indent: -9999px;
}
@media (max-width: 991px) {
  .s2__title {
    width: 250px;
    margin-left: auto;
    margin-right: auto;
    margin-top: 0;
  }
}
.s2__wrap {
  width: 945px;
  margin-left: auto;
  margin-right: auto;
}
@media (max-width: 991px) {
  .s2__wrap {
    width: 100%;
    margin-top: 15px;
  }
}
.s2__content {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  margin: 0px auto;
}
@media (max-width: 1199px) {
  .s2__content {
    width: 100%;
  }
}
@media (max-width: 991px) {
  .s2__content {
    display: block;
    width: 100%;
  }
}
.s2__form {
  width: 576px;
  height: 100%;
  margin-left: 60px;
  font-size: 1.25em;
}
@media (max-width: 1199px) {
  .s2__form {
    width: 550px;
    margin-left: 30px;
  }
}
@media (max-width: 991px) {
  .s2__form {
    width: 100%;
    margin-left: 0;
    margin-top: 15px;
  }
}
.s2__form-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: #ffffff;
  margin-top: 12px;
}
.s2__form-item:first-child {
  margin-top: 0;
}
@media (max-width: 480px) {
  .s2__form-item {
    display: block;
    width: 100%;
    text-align: center;
    height: 100px;
  }
}
.s2__form-text {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 165px;
          flex: 0 0 165px;
  color: #0a3a6e;
  font-weight: 500;
  line-height: 2.9em;
  padding-left: 40px;
  position: relative;
  display: inline-block;
  width: 100%;
}
@media (max-width: 480px) {
  .s2__form-text {
    padding-left: 0px;
  }
}
.s2__form-text::after {
  content: "";
  position: absolute;
  right: -1px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  border-left: 2px solid #f9e68d;
  height: 1.7em;
}
@media (max-width: 480px) {
  .s2__form-text::after {
    content: "";
    position: absolute;
    width: calc(100% - 40px);
    left: 20px;
    border-left: none;
    border-bottom: 1px solid #f9e68d;
  }
}
.s2__form-value {
  color: #555555;
  font-weight: 500;
  padding-left: 24px;
}
@media (max-width: 480px) {
  .s2__form-value {
    padding-left: 0px;
  }
}
.s2__note {
  max-width: 960px;
  margin: 30px auto 0;
  color: #795336;
  line-height: 1.4375em;
}
.s2__note-title {
  position: relative;
  display: block;
  font-weight: 700;
  font-size: 1.125em;
  margin-bottom: 5px;
}
.s2__note-title::after {
  content: "";
  position: absolute;
  width: calc(100% - 7em);
  top: 50%;
  left: 7em;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  border-bottom: 1px solid #795336;
}
.s2__note-list {
  font-weight: 500;
  margin-left: 30px;
}
.s2__note-list li {
  line-height: 1.1;
}
.s2__note-list li::marker {
  color: #795336;
  font-size: 1.5em;
}
.s2__btn {
  background-image: url("../images/s2__btn.png");
  margin: 55px auto 0px auto;
}
@media (max-width: 991px) {
  .s2__btn {
    margin-top: 30px;
  }
}