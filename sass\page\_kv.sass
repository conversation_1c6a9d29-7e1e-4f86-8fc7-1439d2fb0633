// //*-- index --*/
.kv
  $kv: &
  height: 980px
  position: relative
  text-align: center
  background-image: url($path + 'kv_bottom.png'), url($path + 'kv_people.png'), url($path + 'kv_bg.jpg')
  background-size: 100% auto, 1240px auto, cover
  background-position: bottom, calc(50% - 70px) calc(100% - 10px), center
  background-repeat: no-repeat, no-repeat, no-repeat
  @media (max-width:$bk-mblg)
    padding-top: 50px
  &__title
    display: block
    width: 719px
    height: auto
    position: relative
    top: 50%
    left: 50%
    transform: translate(-42%, -74%)
    @media (max-width:$bk-sm)
      margin-top: 57px

    &--desktop
      display: block
      @media (max-width:$bk-sm)
        display: none
    &--mobile
      display: none
      @media (max-width:$bk-sm)
        display: block

  &__subtitle
    position: relative
    color: #3c7d5a
    font-size: $fontsize-30
    font-weight: $fontweight-bold
    background-color: #ffffff
    padding: 2px 30px
    border-radius: 30px
    display: inline-block
    bottom: 100px
    &-text
      &--highlight
        padding: 2px 30px
        color: #ffeeb9
        background-color: #3c7d5a
        border-radius: 30px
