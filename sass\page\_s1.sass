// //*-- s1 --*/
.s1
  $root: &
  width: 100%
  position: relative
  // padding: 80px 0
  background-color: #ffffff

  &__title
    text-align: center
    font-size: $fontsize-40
    font-weight: $fontweight-light
    color: #333333
    // margin-bottom: 60px

  &__grid
    display: grid
    grid-template-columns: repeat(2, 1fr)
    gap: 20px
    // max-width: 1200px
    margin: 0 auto
    @media (max-width: $bk-tb)
      grid-template-columns: 1fr
      // gap: 15px

  &__card
    img
      width: 100%
      height: auto
      display: block
      // border-radius: 20px
      transition: transform 0.3s ease, box-shadow 0.3s ease
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1)

      &:hover
        transform: translateY(-5px)
        // box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15)
