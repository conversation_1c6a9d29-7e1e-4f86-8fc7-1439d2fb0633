// //*-- s1 --*/
.s1
  $root: &
  width: 100%
  position: relative
  padding: 80px 0
  background-color: #f8f9fa

  &__title
    text-align: center
    font-size: $fontsize-36
    font-weight: $fontweight-bold
    color: #333333
    margin-bottom: 60px

  &__grid
    display: grid
    grid-template-columns: repeat(2, 1fr)
    gap: 40px
    max-width: 1200px
    margin: 0 auto
    @media (max-width: $bk-tb)
      grid-template-columns: 1fr
      gap: 30px

  &__card
    border-radius: 20px
    overflow: hidden
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1)
    transition: transform 0.3s ease, box-shadow 0.3s ease
    display: flex
    min-height: 280px
    position: relative

    &:hover
      transform: translateY(-5px)
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15)

    &-image
      flex: 0 0 240px
      position: relative
      overflow: hidden
      display: flex
      align-items: center
      justify-content: center

      img
        width: 80%
        height: 80%
        object-fit: contain

    &-content
      flex: 1
      padding: 30px
      display: flex
      flex-direction: column
      position: relative
      background: white

    &-icon
      position: absolute
      top: 20px
      right: 20px
      width: 40px
      height: 40px

      img
        width: 100%
        height: 100%
        object-fit: contain

    &-title
      font-size: $fontsize-22
      font-weight: $fontweight-bold
      color: #333333
      margin-bottom: 8px

    &-subtitle
      font-size: $fontsize-14
      color: #3c7d5a
      margin-bottom: 15px
      font-weight: $fontweight-medium

    &-features
      list-style: none
      padding: 0
      margin: 0 0 20px 0
      flex: 1

      li
        font-size: $fontsize-14
        color: #666666
        line-height: 1.5
        margin-bottom: 8px
        position: relative
        padding-left: 15px

        &:before
          content: '●'
          color: #3c7d5a
          position: absolute
          left: 0

    &-price
      color: #333333
      padding: 8px 16px
      border-radius: 20px
      font-size: $fontsize-14
      font-weight: $fontweight-bold
      text-align: center
      align-self: flex-end
      min-width: 100px
      margin-top: auto

    // 響應式設計
    @media (max-width: $bk-tb)
      flex-direction: column
      min-height: auto

      &-image
        flex: none
        height: 200px

      &-content
        padding: 20px

      &-icon
        top: 15px
        right: 15px
        width: 35px
        height: 35px

      &-price
        align-self: center
        margin-top: 15px

  // 特殊卡片背景色和價格標籤 - 根據設計稿
  &__card:nth-child(1)
    .s1__card-image
      background: #b8e6d1  // 淺綠色
    .s1__card-price
      background: #a8d5c1  // 深一點的綠色

  &__card:nth-child(2)
    .s1__card-image
      background: #e8d5c4  // 淺棕色
    .s1__card-price
      background: #d8c5b4  // 深一點的棕色

  &__card:nth-child(3)
    .s1__card-image
      background: #c8d8f0  // 淺藍色
    .s1__card-price
      background: #b8c8e0  // 深一點的藍色

  &__card:nth-child(4)
    .s1__card-image
      background: #f0c8d8  // 淺粉色
    .s1__card-price
      background: #e0b8c8  // 深一點的粉色