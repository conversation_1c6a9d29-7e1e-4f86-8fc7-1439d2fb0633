// //*-- s1 --*/
.s1
  $root: &
  width: 100%
  position: relative
  // padding: 80px 0
  background-color: #ffffff

  &__title
    text-align: center
    font-size: $fontsize-40
    font-weight: $fontweight-light
    color: #333333
    margin-top: -40px
    margin-bottom: 56px

  &__grid
    display: grid
    grid-template-columns: repeat(2, 1fr)
    gap: 20px
    margin: 0 auto
    
    @media (max-width: $bk-tb)
      grid-template-columns: 1fr
      gap: 15px

  &__card
    position: relative

    &::after
      content: ''
      position: absolute
      top: -40px
      left: 0
      width: 100%
      height: 100%
      background-image: url($path + 's1_shadow.png')
      background-size: 100% auto
      background-repeat: no-repeat
      background-position: center
      z-index: 1
      opacity: 0.6
      pointer-events: none

    img
      width: 100%
      height: auto
      display: block
      transition: transform 0.3s ease
      position: relative
      z-index: 2

      &:hover
        transform: translateY(-5px)
