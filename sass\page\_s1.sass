// //*-- s1 --*/
.s1
  $root: &
  width: 100%
  height: 100%
  position: relative
  padding-top: 45px
  background: url($path+"s1-bg.jpg")
  background-size: 100% auto
  background-position: center top
  background-repeat: repeat-y

  &__block
    display: inline-flex
    width: calc( 100% + 12px )
    margin-left: -6px
    margin-right: -6px
    text-align: center
    position: relative
    margin-top: 35px
  &__item
    padding-left: 6px
    padding-right: 6px
    @media(max-width: $bk-lg)
      width: 351px
    @media(max-width: $bk-md)
      width: 287px
    @media(max-width: $bk-tb)
      width: 250px
    @media (max-width:$bk-sm)
      padding-bottom: 0
    &-text
      display: block
      color: #0a3a6e
      line-height: 1.6em
      margin-top: 10px
      padding-left: 20px
      padding-right: 50px
      text-align: left
      position: relative
      @media(max-width: $bk-lg)
        padding-left: 10px
      @media (max-width:$bk-md)
        padding-right: 10px
      &--title
        font-size: $fontsize-18
        font-weight: $fontweight-bold
        line-height: 1.6em
        margin-top: 10px
        text-align: left
      &--subtitle
        font-size: $fontsize-18
        line-height: 1.6em
        margin-top: 10px
        text-align: left
      &--icon
        display: inline-block
        width: 17px
        height: 16px
        margin-top: 2px
        background: url($path+"s1__icon-01.png")
        background-size: 100% auto
        background-position: center center
      &-box
        max-height: 4.5em
        line-height: 1.5em
        overflow: hidden
        position: relative
        transition: max-height 0.3s ease
        &.hide::after
          content: ""
          position: absolute
          bottom: 0
          left: 0
          width: 100%
          height: 0.4em
          background: linear-gradient(to top, #f7f7f7, transparent)
          pointer-events: none
      .toggle-btn
        color: #0a3a6e
        border: 1px solid #0a3a6e
        padding: 5px
        font-size: 14px
        cursor: pointer
        position: absolute
        right: 0
        bottom: 0
        z-index: 3
        width: 40px
        line-height: 100%
        text-align: center
        @media (max-width:$bk-md)
          position: relative
          right: auto
          bottom: auto
          width: 100%
          padding: 10px
          margin-top: 10px
        
  &__contents
    width: 100%
    height: 100%
    margin-top: -105px
    margin-left: auto
    margin-right: auto
  &__banner
    position: relative
    display: flex
    height: 100%
    margin: 60px 0 66px
    @media (max-width:$bk-md)
      margin-bottom: 30px
    img
      margin-left: auto
      margin-right: auto
      padding-left: 124px
      padding-right: 124px
      @media(max-width: $bk-tb)
        padding-left: 40px
        padding-right: 40px
      @media(max-width: $bk-sm)
        padding-left: 12px
        padding-right: 12px
    &::before
      content: ""
      position: absolute
      top: -120px
      left: -50px
      width: 512px
      height: 479px
      z-index: 1
      background: url($path+"s1__light-left.png")
      background-position: center center
      background-repeat: no-repeat
      background-size: 100% auto
      pointer-events: none
      @media(max-width: $bk-md)
        width: 400px
        left: -20px
      @media(max-width: $bk-tb)
        left: -70px
      @media(max-width: $bk-sm)
        width: 300px
        top: -190px
        left: -50px
    &::after
      content: ""
      position: absolute
      top: -120px
      right: -50px
      width: 512px
      height: 479px
      background: url($path+"s1__light-right.png")      
      background-position: center center
      background-repeat: no-repeat
      background-size: 100% auto
      pointer-events: none
      @media(max-width: $bk-md)
        width: 400px
        right: -20px
      @media(max-width: $bk-tb)
        right: -70px
      @media(max-width: $bk-sm)
        width: 300px
        top: -190px
        right: -50px
  &__btn
    margin-top: auto 0px
    @media(max-width: $bk-lg)
      width: 300px
    @media(max-width: $bk-md)
      width: 250px
    @media(max-width: $bk-tb)
      width: 340px
      margin-top: 10px
    @media(max-width: $bk-mbsm)
      width: 250px
    &-wrap
      display: flex
      justify-content: center
      align-items: center
      margin: 0px auto
      padding-bottom: 75px
      justify-content: center
      gap: 65px
      @media(max-width: $bk-tb)
        flex-direction: column
        gap: 0
      @media (max-width:$bk-sm)
        padding-bottom: 60px

    &-login
      background-image: url($path+"s1__btn-login.png")
    &-home
      background-image: url($path+"s1__btn-home.png")
.slick
  &-prev
    width: 32px
    height: 32px
    right: 37px
    top: -40px
    @media(max-width: $bk-sm)
      width: 28px
      height: 28px
      left: 5px
      top: -40px
  &-next
    width: 32px
    height: 32px
    right: 5px
    top: -40px
    @media(max-width: $bk-sm)
      width: 28px
      height: 28px
      left: 33px
      top: -40px