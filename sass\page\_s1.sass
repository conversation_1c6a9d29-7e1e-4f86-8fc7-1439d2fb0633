// //*-- s1 --*/
.s1
  $root: &
  width: 100%
  position: relative
  // padding: 80px 0
  background-color: #ffffff

  &__title
    text-align: center
    font-size: $fontsize-40
    font-weight: $fontweight-light
    color: #333333
    margin-top: -40px
    margin-bottom: 56px

  &__grid
    display: grid
    grid-template-columns: repeat(2, 1fr)
    gap: 20px
    margin: 0 auto
    
    @media (max-width: $bk-tb)
      grid-template-columns: 1fr
      gap: 15px

  &__card
    position: relative

    &::after
      content: ''
      position: absolute
      top: -40px
      left: 0
      width: 100%
      height: 100%
      background-image: url($path + 's1_shadow.png')
      background-size: 100% auto
      background-repeat: no-repeat
      background-position: center
      z-index: 1
      opacity: 0.6
      pointer-events: none

    img
      width: 100%
      height: auto
      display: block
      transition: transform 0.3s ease
      position: relative
      z-index: 2

      &:hover
        transform: translateY(-5px)

  &__more
    margin-top: 60px
    display: flex
    align-items: center
    justify-content: space-between
    background: linear-gradient(135deg, #f8f9fa, #e9ecef)
    border-radius: 20px
    padding: 30px 40px
    @media (max-width: $bk-tb)
      flex-direction: column
      text-align: center
      gap: 20px

    &-content
      flex: 1

    &-title
      font-size: $fontsize-24
      font-weight: $fontweight-medium
      color: #333333
      margin-bottom: 15px

    &-tags
      display: flex
      flex-wrap: wrap
      gap: 10px
      @media (max-width: $bk-tb)
        justify-content: center

    &-tag
      background: white
      color: #666666
      padding: 8px 16px
      border-radius: 20px
      font-size: $fontsize-20
      font-weight: $fontweight-medium
      border: 1px solid #e0e0e0
      transition: all 0.3s ease

      &:hover
        background: #6c5ce7
        color: white
        border-color: #6c5ce7
        transform: translateY(-2px)

    &-action
      flex: 0 0 auto

    &-btn
      background: white
      color: #333333
      border: 2px solid #e0e0e0
      padding: 15px 30px
      border-radius: 30px
      font-size: $fontsize-20
      font-weight: $fontweight-medium
      cursor: pointer
      transition: all 0.3s ease
      display: flex
      align-items: center
      gap: 10px

      &:hover
        background: #6c5ce7
        color: white
        border-color: #6c5ce7
        transform: translateY(-2px)

    &-arrow
      font-size: $fontsize-18
      transition: transform 0.3s ease

    &-btn:hover &-arrow
      transform: translateX(5px)
