// //*-- s1 --*/
.s1
  $root: &
  width: 100%
  position: relative
  padding: 80px 0
  background-color: #f8f9fa

  &__title
    text-align: center
    font-size: $fontsize-36
    font-weight: $fontweight-bold
    color: #333333
    margin-bottom: 60px

  &__grid
    display: grid
    grid-template-columns: repeat(2, 1fr)
    gap: 40px
    max-width: 1200px
    margin: 0 auto
    @media (max-width: $bk-tb)
      grid-template-columns: 1fr
      gap: 30px

  &__card
    border-radius: 20px
    overflow: hidden
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1)
    transition: transform 0.3s ease, box-shadow 0.3s ease
    position: relative
    background: white

    &:hover
      transform: translateY(-5px)
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15)

    img
      width: 100%
      height: auto
      display: block
