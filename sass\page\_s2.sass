// //*-- s1 --*/
.s2
  $root: &
  padding-top: 90px
  padding-bottom: 65px
  background-image: url($path+"s2-bg.jpg")
  background-position: center top
  background-repeat: no-repeat
  @media (max-width:$bk-md)
    background-position: left calc( 50vw - 900px ) top
  @media (max-width:$bk-tb)
    padding-top: 1px
    padding-top: 60px
    padding-bottom: 60px
  @media (max-width:$bk-sm)
    background-size: cover
    background-position: left top
  &__title
    width: 308px
    height: 61px
    background: url($path+"s2-title.png")
    background-size: 100% auto
    background-position: center top
    background-repeat: no-repeat
    margin-top: 70px
    text-indent: -9999px
    @media (max-width:$bk-tb)
      width: 250px
      margin-left: auto
      margin-right: auto
      margin-top: 0
  &__wrap
    width: 945px
    margin-left: auto
    margin-right: auto
    @media (max-width:$bk-tb)
      width: 100%
      margin-top: 15px
  &__content
    display: inline-flex
    justify-content: center
    width: 100%
    margin: 0px auto
    @media (max-width:$bk-md)
      width: 100%
    @media (max-width:$bk-tb)
      display: block
      width: 100%
  &__form
    width: 576px
    height: 100%
    margin-left: 60px
    font-size: $fontsize-20
    @media (max-width:$bk-md)
      width: 550px
      margin-left: 30px
    @media (max-width:$bk-tb)
      width: 100%
      margin-left: 0
      margin-top: 15px
    &-item
      display: flex
      align-items: center
      background-color: #ffffff
      margin-top: 12px
      &:first-child
        margin-top: 0
      @media (max-width:$bk-mblg)
        display: block
        width: 100%
        text-align: center
        height: 100px
    &-text
      flex: 0 0 165px
      color: #0a3a6e
      font-weight: $fontweight-medium
      line-height: 2.9em
      padding-left: 40px
      position: relative
      display: inline-block
      width: 100%
      @media (max-width:$bk-mblg)
        padding-left: 0px
      &::after
        content: ''
        position: absolute
        right: -1px
        top: 50%
        transform: translateY(-50%)
        border-left: 2px solid #f9e68d
        height: 1.7em
        @media (max-width:$bk-mblg)
          content: ''
          position: absolute
          width: calc(100% - 40px)
          left: 20px
          border-left: none
          border-bottom: 1px solid #f9e68d
    &-value
      color: #555555
      font-weight: $fontweight-medium
      padding-left: 24px 
      @media (max-width:$bk-mblg)
        padding-left: 0px

  &__note
    max-width: 960px
    margin: 30px auto 0
    color: #795336
    line-height: 1.4375em
    &-title
      position: relative
      display: block
      font-weight: $fontweight-bold
      font-size: $fontsize-18
      margin-bottom: 5px
      &::after
        content: ''
        position: absolute
        width: calc(100% - 7em)
        top: 50%
        left: 7em
        transform: translateY(-50%)
        border-bottom: 1px solid #795336
    &-list
      font-weight: $fontweight-medium
      margin-left: 30px
      li
        line-height: 1.1
        &::marker
          color: #795336
          font-size: $fontsize-24
  &__btn
    background-image: url($path+"s2__btn.png")
    margin: 55px auto 0px auto
    @media (max-width:$bk-tb)
      margin-top: 30px