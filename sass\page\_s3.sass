// //*-- s3 --*/
.s3
  $root: &
  width: 100%
  position: relative
  padding: 80px 0
  background: linear-gradient(135deg, #fef7e6, #fff9f0)

  &__content
    display: grid
    grid-template-columns: 1fr 2fr 1fr
    align-items: center
    gap: 60px
    max-width: 1200px
    margin: 0 auto
    @media (max-width: $bk-tb)
      grid-template-columns: 1fr
      gap: 40px
      text-align: center

  &__title-section
    display: flex
    align-items: center
    justify-content: center

  &__title
    font-size: $fontsize-48
    font-weight: $fontweight-bold
    color: #333333
    text-align: center
    line-height: 1.2
    @media (max-width: $bk-tb)
      font-size: $fontsize-32

  &__illustration
    display: flex
    justify-content: center
    align-items: center

    img
      width: 100%
      max-width: 300px
      height: auto
      display: block
      
  &__services
    display: flex
    flex-direction: column
    gap: 20px
    align-items: center

  &__service
    border-radius: 25px
    padding: 20px 30px
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1)
    transition: transform 0.3s ease, box-shadow 0.3s ease
    cursor: pointer
    text-align: center
    min-width: 220px
    border: 3px solid transparent

    &:hover
      transform: translateY(-3px)
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15)

    &--claim
      background: #ff6b6b
      border-color: #ff6b6b

      .s3__service-label
        color: white

      .s3__service-desc
        color: rgba(255, 255, 255, 0.9)

    &--policy
      background: #4ecdc4
      border-color: #4ecdc4

      .s3__service-label
        color: white

      .s3__service-desc
        color: rgba(255, 255, 255, 0.9)

  &__service-label
    display: block
    font-size: $fontsize-20
    font-weight: $fontweight-bold
    margin-bottom: 8px

  &__service-desc
    font-size: $fontsize-14
    line-height: 1.4
    margin: 0
