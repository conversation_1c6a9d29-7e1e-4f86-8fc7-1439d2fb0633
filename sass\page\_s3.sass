// //*-- s3 --*/
.s3
  $root: &
  width: 100%
  position: relative
  padding: 80px 0
  background: linear-gradient(135deg, #fef7e6, #fff9f0)
  
  &__content
    display: flex
    align-items: center
    gap: 60px
    max-width: 1200px
    margin: 0 auto
    @media (max-width: $bk-tb)
      flex-direction: column
      gap: 40px
      text-align: center
      
  &__illustration
    flex: 0 0 400px
    @media (max-width: $bk-tb)
      flex: none
      max-width: 300px
      
    img
      width: 100%
      height: auto
      display: block
      
  &__main
    flex: 1
    
  &__title
    font-size: $fontsize-36
    font-weight: $fontweight-bold
    color: #333333
    margin-bottom: 40px
    @media (max-width: $bk-tb)
      font-size: $fontsize-28
      margin-bottom: 30px
      
  &__services
    display: flex
    flex-direction: column
    gap: 30px
    
  &__service
    display: flex
    align-items: flex-start
    gap: 20px
    padding: 25px
    background: white
    border-radius: 15px
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08)
    transition: transform 0.3s ease, box-shadow 0.3s ease
    
    &:hover
      transform: translateY(-3px)
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12)
      
    @media (max-width: $bk-tb)
      flex-direction: column
      align-items: center
      text-align: center
      gap: 15px
      
  &__service-icon
    flex: 0 0 60px
    width: 60px
    height: 60px
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e)
    border-radius: 50%
    display: flex
    align-items: center
    justify-content: center
    
    img
      width: 30px
      height: 30px
      object-fit: contain
      
  &__service-content
    flex: 1
    
  &__service-title
    font-size: $fontsize-20
    font-weight: $fontweight-bold
    color: #ff6b6b
    margin-bottom: 8px
    
  &__service-desc
    // font-size: $fontsize-16
    color: #666666
    line-height: 1.6
    margin: 0
